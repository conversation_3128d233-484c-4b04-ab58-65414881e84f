<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KUNPENG Test Portal</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
      color: #ffffff;
      min-height: 100vh;
      overflow-x: hidden;
    }

    /* 科技感背景动画 */
    .tech-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      opacity: 0.1;
    }

    .tech-grid {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }

    /* 头部标题 */
    .header {
      text-align: center;
      padding: 40px 20px;
      background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 122, 204, 0.1));
      border-bottom: 2px solid rgba(0, 255, 255, 0.3);
      position: relative;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
      animation: scanLine 3s ease-in-out infinite;
    }

    @keyframes scanLine {
      0%, 100% { opacity: 0; }
      50% { opacity: 1; }
    }

    .header h1 {
      font-size: 3rem;
      font-weight: 300;
      letter-spacing: 3px;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 10px;
    }

    .header .subtitle {
      font-size: 1.2rem;
      color: rgba(255, 255, 255, 0.7);
      letter-spacing: 1px;
    }

    /* 主要内容区域 */
    .main-container {
      max-width: 1800px;
      margin: 0 auto;
      padding: 40px 20px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 25px;
    }

    @media (max-width: 1200px) {
      .main-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* 模块卡片 */
    .module-card {
      background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 15px;
      padding: 25px;
      backdrop-filter: blur(10px);
      position: relative;
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .module-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
      transition: left 0.5s ease;
    }

    .module-card:hover::before {
      left: 100%;
    }

    .module-card:hover {
      transform: translateY(-5px);
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    }

    .module-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #00ffff;
      text-align: center;
      position: relative;
    }

    .module-title::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 50px;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    /* 按钮样式 */
    .btn-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .tech-btn {
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.2), rgba(0, 255, 255, 0.1));
      border: 1px solid rgba(0, 255, 255, 0.3);
      color: #ffffff;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: block;
      text-align: center;
      font-size: 0.9rem;
      position: relative;
      overflow: hidden;
    }

    .tech-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.3s ease;
    }

    .tech-btn:hover::before {
      left: 100%;
    }

    .tech-btn:hover {
      background: linear-gradient(45deg, rgba(0, 122, 204, 0.4), rgba(0, 255, 255, 0.2));
      border-color: rgba(0, 255, 255, 0.6);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
    }

    .tech-btn.disabled {
      background: linear-gradient(45deg, rgba(100, 100, 100, 0.2), rgba(150, 150, 150, 0.1));
      border-color: rgba(150, 150, 150, 0.3);
      color: rgba(255, 255, 255, 0.5);
      cursor: not-allowed;
    }

    .tech-btn.disabled:hover {
      transform: none;
      box-shadow: none;
    }

    /* 状态指示器 */
    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-active {
      background: #00ff00;
      box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
      animation: pulse 2s infinite;
    }

    .status-inactive {
      background: #ff6b6b;
      box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .main-container {
        grid-template-columns: 1fr;
        padding: 20px 10px;
      }
      
      .btn-grid {
        grid-template-columns: 1fr;
      }
    }

    /* 模态框样式 */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background: linear-gradient(145deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
      margin: 5% auto;
      padding: 30px;
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      text-align: center;
      position: relative;
    }

    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      position: absolute;
      right: 15px;
      top: 10px;
      cursor: pointer;
    }

    .close:hover {
      color: #00ffff;
    }
  </style>
</head>
<body>
  <div class="tech-bg">
    <div class="tech-grid"></div>
  </div>

  <div class="header">
    <h1>KUNPENG Test Portal</h1>
    <div class="subtitle">Advanced Testing & Monitoring Dashboard</div>
  </div>

  <div class="main-container">
    <!-- MR状态模块 -->
    <div class="module-card">
      <h2 class="module-title">MR 状态监控</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="mrStatus" data-key="R1.0">
          <span class="status-indicator status-inactive"></span>R1.0
        </a>
        <a href="#" class="tech-btn" data-module="mrStatus" data-key="R1.2">
          <span class="status-indicator status-inactive"></span>R1.2
        </a>
        <a href="#" class="tech-btn" data-module="mrStatus" data-key="R1.3">
          <span class="status-indicator status-inactive"></span>R1.3
        </a>
        <a href="#" class="tech-btn" data-module="mrStatus" data-key="R1.4">
          <span class="status-indicator status-active"></span>R1.4
        </a>
      </div>
    </div>

    <!-- 性能监控模块 -->
    <div class="module-card">
      <h2 class="module-title">性能监控系统</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="nesummary">
          <span class="status-indicator status-active"></span>NE Summary
        </a>
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="cpuMemory">
          <span class="status-indicator status-inactive"></span>CPU Memory
        </a>
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="hardDisk">
          <span class="status-indicator status-inactive"></span>HardDisk
        </a>
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="slot">
          <span class="status-indicator status-inactive"></span>Slot
        </a>
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="port">
          <span class="status-indicator status-inactive"></span>Port
        </a>
        <a href="#" class="tech-btn" data-module="performanceMonitoring" data-key="alarm">
          <span class="status-indicator status-inactive"></span>Alarm
        </a>
      </div>
    </div>

    <!-- 测试执行模块 -->
    <div class="module-card">
      <h2 class="module-title">测试执行中心</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="testExecution" data-key="avatar">
          <span class="status-indicator status-active"></span>Avatar
        </a>
        <a href="#" class="tech-btn" data-module="testExecution" data-key="goat">
          <span class="status-indicator status-active"></span>Goat
        </a>
        <a href="#" class="tech-btn" data-module="testExecution" data-key="sanity">
          <span class="status-indicator status-active"></span>Sanity
        </a>
      </div>
    </div>

    <!-- 测试报告模块 -->
    <div class="module-card">
      <h2 class="module-title">测试报告中心</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="testReports" data-key="avatarReport">
          <span class="status-indicator status-active"></span>Avatar Report
        </a>
        <a href="#" class="tech-btn" data-module="testReports" data-key="goatReport">
          <span class="status-indicator status-active"></span>Goat Report
        </a>
        <a href="#" class="tech-btn" data-module="testReports" data-key="sanityReport">
          <span class="status-indicator status-active"></span>Sanity Report
        </a>
      </div>
    </div>

    <!-- 测试资源模块 -->
    <div class="module-card">
      <h2 class="module-title">测试资源管理</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="testResources" data-key="codeManagement">
          <span class="status-indicator status-active"></span>代码管理
        </a>
        <a href="#" class="tech-btn" data-module="testResources" data-key="testEnvironment">
          <span class="status-indicator status-active"></span>测试环境
        </a>
        <a href="#" class="tech-btn" data-module="testResources" data-key="testTopology">
          <span class="status-indicator status-inactive"></span>测试拓扑
        </a>
        <a href="#" class="tech-btn" data-module="testResources" data-key="serialPortFSV1">
          <span class="status-indicator status-active"></span>串口管理FSV1
        </a>
        <a href="#" class="tech-btn" data-module="testResources" data-key="serialPortFSV2">
          <span class="status-indicator status-active"></span>串口管理FSV2
        </a>
        <a href="#" class="tech-btn" data-module="testResources" data-key="emsServer">
          <span class="status-indicator status-active"></span>EMS Server
        </a>
      </div>
    </div>

    <!-- 测试文档模块 -->
    <div class="module-card">
      <h2 class="module-title">测试文档中心</h2>
      <div class="btn-grid">
        <a href="#" class="tech-btn" data-module="testDocuments" data-key="productDoc">
          <span class="status-indicator status-inactive"></span>产品文档
        </a>
        <a href="#" class="tech-btn" data-module="testDocuments" data-key="requirementDoc">
          <span class="status-indicator status-inactive"></span>需求文档
        </a>
      </div>
    </div>
  </div>

  <!-- 模态框 -->
  <div id="errorModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h3 style="color: #ff6b6b; margin-bottom: 20px;">页面不存在</h3>
      <p>抱歉，该功能页面暂未配置或不存在。</p>
      <p style="margin-top: 15px; color: rgba(255,255,255,0.7);">请联系管理员进行配置。</p>
    </div>
  </div>

  <script>
    let config = {};

    // 加载配置文件
    async function loadConfig() {
      try {
        const response = await fetch('config.json');
        config = await response.json();
        console.log('配置加载成功:', config);
        updateButtonStates();
      } catch (error) {
        console.error('配置加载失败:', error);
      }
    }

    // 更新按钮状态
    function updateButtonStates() {
      document.querySelectorAll('.tech-btn').forEach(button => {
        const module = button.dataset.module;
        const key = button.dataset.key;
        const statusIndicator = button.querySelector('.status-indicator');

        if (config[module] && config[module][key] && config[module][key].trim() !== '') {
          statusIndicator.classList.remove('status-inactive');
          statusIndicator.classList.add('status-active');
        } else {
          statusIndicator.classList.remove('status-active');
          statusIndicator.classList.add('status-inactive');
        }
      });
    }

    // 智能判断是否为iframe链接
    function isIframeUrl(url) {
      // 检查是否包含embed参数或常见的仪表板关键词
      const iframeIndicators = [
        'embed=true',
        'iframe',
        '/dashboards#/view/',
        '/app/dashboards',
        'kibana',
        'grafana'
      ];

      return iframeIndicators.some(indicator =>
        url.toLowerCase().includes(indicator.toLowerCase())
      );
    }

    // 处理按钮点击
    function handleButtonClick(event) {
      event.preventDefault();

      const button = event.currentTarget;
      const module = button.dataset.module;
      const key = button.dataset.key;

      if (!config[module] || !config[module][key]) {
        showErrorModal();
        return;
      }

      const url = config[module][key];
      if (!url) {
        showErrorModal();
        return;
      }

      // 智能判断链接类型
      if (isIframeUrl(url)) {
        // iframe链接，创建科技感仪表板页面
        createTechDashboardPage(url, key);
      } else if (url.endsWith('.html')) {
        // HTML文件直接在新窗口打开
        window.open(url, '_blank');
      } else {
        // 其他链接直接跳转
        window.open(url, '_blank');
      }
    }

    // 显示错误模态框
    function showErrorModal() {
      document.getElementById('errorModal').style.display = 'block';
    }

    // 关闭模态框
    function closeModal() {
      document.getElementById('errorModal').style.display = 'none';
    }

    // 创建科技感仪表板页面
    function createTechDashboardPage(url, title) {
      const newWindow = window.open('', '_blank');
      newWindow.document.write('<!DOCTYPE html><html lang="zh"><head><meta charset="UTF-8"><title>KUNPENG Tech Dashboard - ' + title + '</title>');
      newWindow.document.write('<style>');
      newWindow.document.write('* { margin: 0; padding: 0; box-sizing: border-box; }');
      newWindow.document.write('html, body { height: 100%; font-family: "Segoe UI", "Microsoft YaHei", sans-serif; background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%); color: #ffffff; overflow: hidden; }');
      newWindow.document.write('.tech-bg { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; opacity: 0.1; }');
      newWindow.document.write('.tech-grid { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px); background-size: 50px 50px; animation: gridMove 20s linear infinite; }');
      newWindow.document.write('@keyframes gridMove { 0% { transform: translate(0, 0); } 100% { transform: translate(50px, 50px); } }');
      newWindow.document.write('.header { background: linear-gradient(90deg, rgba(0, 255, 255, 0.1), rgba(0, 122, 204, 0.2)); border-bottom: 2px solid rgba(0, 255, 255, 0.3); padding: 15px 30px; position: relative; backdrop-filter: blur(10px); }');
      newWindow.document.write('.header::before { content: ""; position: absolute; top: 0; left: 0; right: 0; height: 2px; background: linear-gradient(90deg, transparent, #00ffff, transparent); animation: scanLine 3s ease-in-out infinite; }');
      newWindow.document.write('@keyframes scanLine { 0%, 100% { opacity: 0; } 50% { opacity: 1; } }');
      newWindow.document.write('.header-title { font-size: 1.5rem; font-weight: 300; letter-spacing: 2px; text-shadow: 0 0 20px rgba(0, 255, 255, 0.5); display: flex; align-items: center; }');
      newWindow.document.write('.status-dot { width: 12px; height: 12px; border-radius: 50%; background: #00ff00; margin-right: 15px; box-shadow: 0 0 15px rgba(0, 255, 0, 0.7); animation: pulse 2s infinite; }');
      newWindow.document.write('@keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }');
      newWindow.document.write('.content { height: calc(100vh - 80px); padding: 20px; position: relative; }');
      newWindow.document.write('.dashboard-container { height: 100%; border: 2px solid rgba(0, 255, 255, 0.3); border-radius: 15px; background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02)); backdrop-filter: blur(10px); overflow: hidden; position: relative; }');
      newWindow.document.write('.dashboard-container::before { content: ""; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent); animation: shimmer 3s ease-in-out infinite; }');
      newWindow.document.write('@keyframes shimmer { 0% { left: -100%; } 100% { left: 100%; } }');
      newWindow.document.write('.dashboard-header { padding: 15px 25px; font-size: 1.1rem; font-weight: 600; color: #00ffff; background: linear-gradient(90deg, rgba(0, 122, 204, 0.3), rgba(0, 255, 255, 0.2)); border-bottom: 1px solid rgba(0, 255, 255, 0.3); position: relative; }');
      newWindow.document.write('.dashboard-header::after { content: ""; position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; background: linear-gradient(90deg, transparent, #00ffff, transparent); }');
      newWindow.document.write('iframe { width: 100%; height: calc(100% - 60px); border: none; display: block; background: rgba(0, 0, 0, 0.1); }');
      newWindow.document.write('.loading { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 1.2rem; color: #00ffff; text-shadow: 0 0 10px rgba(0, 255, 255, 0.5); z-index: 10; }');
      newWindow.document.write('.loading::after { content: ""; display: inline-block; width: 20px; height: 20px; border: 2px solid rgba(0, 255, 255, 0.3); border-top: 2px solid #00ffff; border-radius: 50%; animation: spin 1s linear infinite; margin-left: 10px; }');
      newWindow.document.write('@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }');
      newWindow.document.write('</style></head><body>');
      newWindow.document.write('<div class="tech-bg"><div class="tech-grid"></div></div>');
      newWindow.document.write('<div class="header"><div class="header-title"><div class="status-dot"></div>KUNPENG Tech Dashboard - ' + title + '</div></div>');
      newWindow.document.write('<div class="content"><div class="dashboard-container">');
      newWindow.document.write('<div class="dashboard-header">' + title + ' - 实时监控面板</div>');
      newWindow.document.write('<div class="loading" id="loading">数据加载中</div>');
      newWindow.document.write('<iframe src="' + url + '" onload="document.getElementById(\'loading\').style.display=\'none\'"></iframe>');
      newWindow.document.write('</div></div>');
      newWindow.document.write('<script>setTimeout(function() { var loading = document.getElementById("loading"); if (loading && loading.style.display !== "none") { loading.style.display = "none"; } }, 5000);</script>');
      newWindow.document.write('</body></html>');
      newWindow.document.close();
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadConfig();
      
      // 绑定按钮点击事件
      document.querySelectorAll('.tech-btn').forEach(button => {
        button.addEventListener('click', handleButtonClick);
      });
      
      // 绑定模态框关闭事件
      document.querySelector('.close').addEventListener('click', closeModal);
      
      // 点击模态框外部关闭
      window.addEventListener('click', function(event) {
        const modal = document.getElementById('errorModal');
        if (event.target === modal) {
          closeModal();
        }
      });
    });
  </script>
</body>
</html>
