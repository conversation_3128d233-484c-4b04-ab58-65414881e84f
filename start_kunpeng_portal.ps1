# KUNPENG Test Portal 启动脚本 (PowerShell版本)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    KUNPENG Test Portal 启动脚本" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置工作目录
$workDir = "c:\test_tools\kunpeng_html"
Set-Location $workDir
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Green
Write-Host ""

# 检查必要文件
Write-Host "检查必要文件..." -ForegroundColor Yellow
$requiredFiles = @("kunpeng_portal_final.html", "config.json")
$allFilesExist = $true

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $file 不存在" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Host "错误: 缺少必要文件！" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}
Write-Host ""

# 检查端口8080
Write-Host "检查端口8080状态..." -ForegroundColor Yellow
$portInUse = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue

if ($portInUse) {
    Write-Host "✓ 端口8080已被使用（服务器可能已运行）" -ForegroundColor Green
} else {
    Write-Host "! 端口8080未被使用，启动HTTP服务器..." -ForegroundColor Yellow
    Start-Process -FilePath "python" -ArgumentList "-m", "http.server", "8080" -WindowStyle Normal
    Write-Host "等待服务器启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
}
Write-Host ""

# 测试服务器连接
Write-Host "测试服务器连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/kunpeng_portal_final.html" -Method Head -TimeoutSec 5
    Write-Host "✓ 服务器响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "! 服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 打开网页
Write-Host "正在打开KUNPENG Test Portal..." -ForegroundColor Yellow
try {
    Start-Process "http://localhost:8080/kunpeng_portal_final.html"
    Write-Host "✓ 网页已成功打开！" -ForegroundColor Green
} catch {
    Write-Host "! 打开网页失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "操作完成！" -ForegroundColor Green
Write-Host "网页地址: http://localhost:8080/kunpeng_portal_final.html" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
