写一个KUNPENG Test Portal的主页面，要有科技感

里面分多个块
1 MR状态
  R1.0 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.2 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.3 对应链接从json文件读取，默认为空，提示用户页面不存在
  R1.4 点击后转到 kunpeng_readmine_issue_status_v2.html 页面
2 性能监控
  NE Summary 对应链接 <iframe src="http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true" height="600" width="800"></iframe>
  CPU Memory 对应链接
  HardDisk   对应链接
  Slot 对应链接从json文件读取，默认为空，提示用户页面不存在
  Port 对应链接从json文件读取，默认为空，提示用户页面不存在
  Alarm 对应链接从json文件读取，默认为空，提示用户页面不存在

3 测试执行：
  Avatar： 直接打开http://100.120.182.16:8088/view/Avatar/
  Goat：   直接打开http://100.120.182.16:8088/view/PSIS/
  Sanity： 直接打开http://100.120.182.16:8088/view/PSIS_Sanity/
  
4 测试报告
  Avatar Report 直接打开 http://100.120.180.253:8080/ui/#avatar_demo/launches/all
  Goat Report  直接打开http://100.120.180.253:8080/ui/#kunpeng/launches/all
  Sanity Report 直接打开 http://100.120.180.253:8080/ui/#avatar_demo/launches/67

5 测试资源
  代码管理：直接打开 http://100.120.182.21/jwang089/goat
  测试环境：直接打开 http://100.120.180.253:8080/ui/#kunpeng/launches/all
  测试拓扑：对应链接从json文件读取，默认为空，提示用户页面不存在
  
    

6 测试文档
  产品文档： 对应链接从json文件读取，默认为空，提示用户页面不存在
  需求文档： 对应链接从json文件读取，默认为空，提示用户页面不存在
  
  
json文件配置，有四种类型

1 直接打开的链接放到json文件里面 http://100.120.182.21/jwang089/goat
2 对应链接从json文件读取，默认为空，提示用户页面不存在，在json文件里面写空
3 点击后转到 kunpeng_readmine_issue_status_v2.html 页面， 在json文件写kunpeng_readmine_issue_status_v2.html
3 有iframe链接，实时生成页面打开，页面内容参考kunpeng_readmine_issue_status_v2.html
<iframe src="http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true" height="600" width="800"></iframe>

##############################

界面改成每行三列
测试资源模块增加 
  串口管理FSV： 直接打开 http://100.120.180.137:7654/consoles/production.xml
  串口管理FSV： 直接打开 http://100.120.180.196:7654/console/production.xml
  
测试资源增加
  EMS Server：直接打开 http://100.120.182.86:8002/login
  
程序根据json里面的链接能否自动判断是网页连接还是ifram连接，如果是iframe链接，实时生成页面打开，页面内容参考kunpeng_readmine_issue_status_v2.html，但也要变得有科技感，页面炫酷

  
########################################################################

修改代码，实现修改json后主页面变化的功能
例如性能监控里面增加一个
CPU Memory summary： 对应链接 <iframe src="http://100.120.180.251:5601/app/dashboards#/view/53fe5349-1953-42ad-9917-dd20e03d01ba?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-5h%2Cto%3Anow))&show-query-input=true&show-time-filter=true" height="600" width="800"></iframe>

根据iframe链接实时生成的页面内容需要参考kunpeng_readmine_issue_status_v2.html，但也要变得有科技感，页面炫酷



###################################
主页面上的
      KUNPENG Test Portal
Advanced Testing & Monitoring Dashboard

字体要变大，颜色要炫酷，跟主页面风格一致