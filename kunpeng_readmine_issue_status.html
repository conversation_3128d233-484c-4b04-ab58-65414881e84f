<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>KUNPENG Project Dashboards</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #f0f4f8, #ffffff);
    }

    .tabs {
      display: flex;
      background-color: #ffffff;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      padding: 10px;
    }

    .tab {
      padding: 10px 24px;
      margin-right: 10px;
      background: linear-gradient(#e0e0e0, #dcdcdc);
      border: none;
      border-radius: 12px 12px 0 0;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s, transform 0.2s, color 0.3s;
    }

    .tab:hover {
      background: #cccccc;
      transform: scale(1.05);
    }

    .tab.active {
      background: linear-gradient(90deg, #007acc, #3399ff);
      color: #ffffff;
      box-shadow: 0 4px 10px rgba(0, 122, 204, 0.3);
    }

    .content {
      height: calc(100% - 60px);
      border-radius: 0 0 12px 12px;
      overflow: hidden;
      position: relative;
    }

    iframe {
      width: 100%;
      height: 100%;
      border: none;
      display: none;
    }

    iframe.active {
      display: block;
    }

    #loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 18px;
      background: rgba(255, 255, 255, 0.8);
      padding: 20px 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: none;
    }
  </style>
</head>
<body>
  <div class="tabs">
    <button class="tab active" onclick="switchTab(0)">kunpeng_issus_trend_report</button>
    <button class="tab" onclick="switchTab(1)">kunpeng_issue_daily_report</button>
    <button class="tab" onclick="switchTab(2)">kunpeng_issue_total</button>
  </div>

  <div class="content">
    <div id="loading">加载中，请稍候...</div>

    <iframe class="active"
      src="http://***************:5601/app/dashboards#/view/563eb913-97ab-4b1e-8d46-b62372aeb7b1?embed=true&_g=(refreshInterval%3A(pause%3A!f%2Cvalue%3A14400000)%2Ctime%3A(from%3Anow-3M%2Cto%3Anow))"
      loading="lazy" allowfullscreen>
    </iframe>

    <iframe
      src="http://***************:5601/app/dashboards#/view/97d66c22-e352-434a-abf7-3caacafa9f31?embed=true&_g=(refreshInterval%3A(pause%3A!t%2Cvalue%3A60000)%2Ctime%3A(from%3Anow-2d%2Cto%3Anow))"
      loading="lazy" allowfullscreen>
    </iframe>

    <iframe
      src="http://***************:5601/app/dashboards#/view/0ed504d6-e5b3-4f62-bcd7-69189a749c16?embed=true&_g=(refreshInterval%3A(pause%3A!f%2Cvalue%3A14400000)%2Ctime%3A(from%3Anow-5d%2Cto%3Anow))"
      loading="lazy" allowfullscreen>
    </iframe>
  </div>

  <script>
    function switchTab(index) {
      const tabs = document.querySelectorAll('.tab');
      const iframes = document.querySelectorAll('iframe');
      const loader = document.getElementById('loading');

      // 显示加载提示
      loader.style.display = 'block';

      // 切换 tab 和 iframe
      tabs.forEach((tab, i) => {
        tab.classList.toggle('active', i === index);
        iframes[i].classList.toggle('active', i === index);
      });

      // 等待几百毫秒再隐藏加载提示
      setTimeout(() => {
        loader.style.display = 'none';
      }, 800);
    }
  </script>
</body>
</html>
