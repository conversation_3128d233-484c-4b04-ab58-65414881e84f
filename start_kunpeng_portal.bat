@echo off
echo ========================================
echo    KUNPENG Test Portal 启动脚本
echo ========================================
echo.

:: 设置工作目录
cd /d "c:\test_tools\kunpeng_html"
echo 当前目录: %CD%
echo.

:: 检查必要文件
echo 检查必要文件...
if not exist "kunpeng_portal_final.html" (
    echo 错误: kunpeng_portal_final.html 文件不存在！
    pause
    exit /b 1
)

if not exist "config.json" (
    echo 错误: config.json 文件不存在！
    pause
    exit /b 1
)

echo ✓ 所有必要文件存在
echo.

:: 检查端口8080是否被占用
echo 检查端口8080状态...
netstat -an | findstr :8080 > nul
if %errorlevel% == 0 (
    echo ✓ 端口8080已被使用（服务器可能已运行）
) else (
    echo ! 端口8080未被使用，需要启动服务器
    echo 启动HTTP服务器...
    start "KUNPENG HTTP Server" cmd /k "python -m http.server 8080"
    echo 等待服务器启动...
    timeout /t 3 /nobreak > nul
)
echo.

:: 测试服务器连接
echo 测试服务器连接...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/kunpeng_portal_final.html
echo.
echo.

:: 打开网页
echo 正在打开KUNPENG Test Portal...
powershell -Command "Start-Process 'http://localhost:8080/kunpeng_portal_final.html'"

if %errorlevel% == 0 (
    echo ✓ 网页已成功打开！
) else (
    echo ! 使用备用方法打开网页...
    start http://localhost:8080/kunpeng_portal_final.html
)

echo.
echo ========================================
echo 操作完成！
echo 网页地址: http://localhost:8080/kunpeng_portal_final.html
echo ========================================
echo.
echo 按任意键退出...
pause > nul
