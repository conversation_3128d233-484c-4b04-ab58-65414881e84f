<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>Debug Test</title>
</head>
<body>
  <button id="testBtn" data-module="mrStatus" data-key="R1.4">Test R1.4</button>
  
  <script>
    let config = {};

    // 加载配置文件
    async function loadConfig() {
      try {
        const response = await fetch('config.json');
        config = await response.json();
        console.log('配置加载成功:', config);
      } catch (error) {
        console.error('配置加载失败:', error);
      }
    }

    // 处理按钮点击
    function handleButtonClick(event) {
      event.preventDefault();
      
      const button = event.currentTarget;
      const module = button.dataset.module;
      const key = button.dataset.key;
      
      console.log('点击按钮:', module, key);
      
      if (!config[module] || !config[module][key]) {
        console.log('配置不存在');
        alert('配置不存在');
        return;
      }
      
      const url = config[module][key];
      console.log('URL:', url);
      
      if (!url) {
        console.log('URL为空');
        alert('URL为空');
        return;
      }
      
      if (url.endsWith('.html')) {
        console.log('打开HTML文件:', url);
        window.open(url, '_blank');
      } else {
        console.log('打开其他链接:', url);
        window.open(url, '_blank');
      }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadConfig();
      
      document.getElementById('testBtn').addEventListener('click', handleButtonClick);
    });
  </script>
</body>
</html>
